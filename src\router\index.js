import { createRouter, createWebHistory } from 'vue-router'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Index',
      redirect: '/project',
      component: () => import('@/views/index.vue'),
      children: [
        {
          path: '/project',
          name: 'project',
          component: () => import('@/views/project/index.vue')
        },
        {
          path: '/model',
          name: 'model',
          component: () => import('@/views/model/index.vue')
        },
        {
          path: '/method',
          name: 'method',
          component: () => import('@/views/method/index.vue')
        },
        {
          path: '/dictionary',
          name: 'dictionary',
          component: () => import('@/views/dictionary/index.vue')
        }
      ]
    }
  ]
})

export default router
