import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import autoImport from "unplugin-auto-import/vite";
// import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    autoImport({
      imports: ["vue", "vue-router", "pinia"],
      dts: false,
    }),
    // vueDevTools(),
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    port: 80,
    host: true,
    open: true,
    proxy: {
      // https://cn.vitejs.dev/config/#server-proxy
      "/dev-api": {
        target: "https://rule.hongfeida.cn",
        // target: "https://m1.apifoxmock.com/m1/6194657-5887230-default",
        changeOrigin: true,
        rewrite: (p) => p.replace(/^\/dev-api/, ""),
      },
    },
  },
  css: {
    loaderOptions: {
      scss: {
        sassOptions: {
          outputStyle: "expanded"
        },
        prependData: `@import "@/assets/styles/element/index.scss";` //在这里全局引入
      }
    }
  }
});
