import request from "@/utils/request";

export function queryProjectGroupList(params) {
  return request({
    url: "/projectGroup/queryProjectGroupList",
    method: "get",
    params,
  });
}

/**
 * 添加项目分组
 * @param {Object} data - 项目分组数据
 * @returns {Promise} 返回一个Promise对象，包含请求结果
 */
export function addProjectGroup(data) {
  return request({
    url: "/projectGroup/addProjectGroup", // 请求URL地址
    method: "post", // 请求方法为POST
    data, // 请求数据
  });
}
/**
 * 更新项目分组信息
 * @param {Object} data - 包含项目分组更新信息的数据对象
 * @returns {Promise} - 返回一个Promise对象，用于处理请求结果
 */
export function updateProjectGroup(data) {
  return request({
    url: "/projectGroup/updateProjectGroup", 
    method: "post", 
    data, 
  });
}
export function deleteProjectGroup(data) {
  return request({
    url: "/projectGroup/deleteProjectGroup", 
    method: "delete", 
    params:data, 
  });
}

/**
 * 添加项目的方法
 * @param {Object} data - 项目数据对象
 * @returns {Promise} - 返回一个Promise对象，包含请求结果
 */
export function addProject(data) {
  return request({
    url: "/project/addProject", // 请求URL地址
    method: "post", // 请求方法为POST
    data, // 请求数据
  });
}



// 通过项目id查询项目下模块、预处理数据、规制集
export  function  queryProjectItemInfo(params){
  return request({
    url: "/project/queryProjectItemInfo",
    method: "get",
    params,
  });
}
export function updateProject(data) {
  return request({
    url: "/project/updateProject", // 请求URL地址
    method: "post", // 请求方法为POST
    data, // 请求数据
  });
}
export function deleteProject(data) {
  return request({
    url: "/project/deleteProject", 
    method: "get", 
    params:data, 
  });
}
