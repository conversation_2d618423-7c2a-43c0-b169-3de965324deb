import useOperatorStore from '@/stores/modules/operator'
import useModelStore from '@/stores/modules/model'
import useMethodStore from '@/stores/modules/method'

export default function() {
  const operatorStore = useOperatorStore()
  const modelStore = useModelStore()
  const methodStore = useMethodStore()

  function getAllData(modelIds, methodIds) {
    operatorStore.getOperator()
    methodStore.getModels(methodIds)
    modelStore.getModels(modelIds)
  }

  function addModel(id) {

  }

  function deleteModel(id) {

  }

  function addMethod(id) {

  } 

  function deleteMethod(id) {

  }

  return {operatorStore, methodStore, modelStore, getAllData, addModel, deleteModel, addMethod, deleteMethod}

}