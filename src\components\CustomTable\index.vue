<template>
  <el-table
    ref="tableRef"
    v-bind="$attrs"
    :data="tableData"
    :border="border"
    :stripe="stripe"
    v-loading="loading"
    row-key="id"
    empty-text="暂无数据"
    @selection-change="handleSelectionChange"
  >
    <el-table-column width="30" align="center" v-if="draggable">
      <template #default>
        <img class="cursor-move" src="../../assets/images/drag.svg" alt="" />
      </template>
    </el-table-column>
    <template v-for="item in columns" :key="item.prop">
      <!-- 选择列 -->
      <el-table-column
        v-if="item.type === 'selection'"
        type="selection"
        width="55"
      />

      <!-- 序号列 -->
      <el-table-column
        v-else-if="item.type === 'index'"
        type="index"
        :align="item.align || 'center'"
        :width="item.width || '80'"
        :label="item.label || '序号'"
      />

      <!-- 普通列 -->
      <el-table-column
        v-else
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :min-width="item.minWidth"
        :fixed="item.fixed"
        :align="item.align || 'center'"
      >
        <!-- 自定义列内容 -->
        <template #default="scope" v-if="item.slot">
          <slot :name="item.slot" :row="scope.row"></slot>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>

<script setup>
import Sortable from "sortablejs";
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  columns: {
    type: Array,
    required: true,
  },
  tableData: {
    type: Array,
    default: () => [],
    required: true,
  },
  border: {
    type: Boolean,
    default: false,
  },
  stripe: {
    type: Boolean,
    default: false,
  },
  draggable: {
    type: Boolean,
    default: false,
  },
  dragOptions: {
    type: Object,
    default: () => ({
      animation: 180,
      ghostClass: "drag-ghost",
      chosenClass: "drag-chosen",
      dragClass: "drag-drag",
    }),
  },
});
const tableRef = ref(null);
let sortableInstance = null;

const emit = defineEmits(["selection-change", "draggable-change"]);

const handleSelectionChange = (val) => {
  emit("selection-change", val);
};

const initRowDraggable = async () => {
  if (!props.draggable || !tableRef.value) return;
  await nextTick();
  try {
    const tbody = tableRef.value.$el.querySelector(
      ".el-table__body-wrapper tbody"
    );
    if (!tbody) {
      console.warn("CustomTable: 无法找到表格tbody元素");
      return;
    }
    if (sortableInstance) {
      sortableInstance.destroy();
    }
    sortableInstance = Sortable.create(tbody, {
      animation: 180,
      animation: props.dragOptions.animation,
      ghostClass: props.dragOptions.ghostClass,
      chosenClass: props.dragOptions.chosenClass,
      dragClass: props.dragOptions.dragClass,
      handle: ".cursor-move",
      onEnd: ({ newIndex, oldIndex }) => {
        if (newIndex !== oldIndex) {
          emit("draggable-change", newIndex, oldIndex);
        }
      },
    });
  } catch (error) {
    console.error("CustomTable: 初始化拖拽功能失败", error);
  }
};
const destroyDraggable = () => {
  if (sortableInstance) {
    sortableInstance.destroy();
    sortableInstance = null;
  }
};

// 监听tableData变化，重新初始化拖拽
watch(
  () => props.tableData,
  () => {
    if (props.draggable) {
      nextTick(() => {
        initRowDraggable();
      });
    }
  },
  { deep: true }
);

onMounted(() => {
  if (props.draggable) {
    initRowDraggable();
  }
});

onUnmounted(() => {
  destroyDraggable();
});
</script>
<style lang="scss" scoped>
.cursor-move {
  margin-top: 4px;
  height: 14px;
  cursor: move;
}
/* 拖拽时的样式 */
:deep(.drag-ghost) {
  opacity: 0.5;
  background-color: var(--el-color-primary-light-9);
}

:deep(.drag-chosen) {
  background-color: var(--el-color-primary-light-8);
}

:deep(.drag-drag) {
  background-color: var(--el-color-primary-light-7);
}

/* 拖拽时禁用文本选择 */
:deep(.sortable-drag) {
  user-select: none;
}
</style>
