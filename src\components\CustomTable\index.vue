<template>
  <el-table
    v-bind="$attrs"
    :data="tableData"
    :border="border"
    :stripe="stripe"
    v-loading="loading"
    row-key="id"
    @selection-change="handleSelectionChange"
  >
    <el-table-column width="30" align="center" v-if="draggable">
      <template #default>
        <el-icon class="cursor-move"><Sort /></el-icon>
      </template>
    </el-table-column>
    <template v-for="item in columns" :key="item.prop">
      <!-- 选择列 -->
      <el-table-column
        v-if="item.type === 'selection'"
        type="selection"
        width="55"
      />

      <!-- 序号列 -->
      <el-table-column
        v-else-if="item.type === 'index'"
        type="index"
        :width="item.width || '80'"
        :label="item.label || '序号'"
      />

      <!-- 普通列 -->
      <el-table-column
        v-else
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        :min-width="item.minWidth"
        :fixed="item.fixed"
        :align="item.align || 'center'"
      >
        <!-- 自定义列内容 -->
        <template #default="scope" v-if="item.slot">
          <slot :name="item.slot" :row="scope.row"></slot>
        </template>
      </el-table-column>

    </template>
  </el-table>
</template>

<script setup>
import Sortable from "sortablejs";
import { onMounted } from "vue";
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  columns: {
    type: Array,
    required: true,
  },
  tableData: {
    type: Array,
    required: true,
  },
  border: {
    type: Boolean,
    default: false,
  },
  stripe: {
    type: Boolean,
    default: false,
  },
  draggable: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["selection-change"]);

const handleSelectionChange = (val) => {
  emit("selection-change", val);
};

const rowDraggable = () => {
  const elTable = document.querySelector(".el-table__body-wrapper tbody");
  Sortable.create(elTable, {
    animation: 180,
    handle: ".cursor-move",
    onEnd: ({ newIndex, oldIndex }) => {
      console.log(111);
      
      const currRow = props.tableData.splice(oldIndex, 1)[0];
      props.tableData.splice(newIndex, 0, currRow);
    },
  });
};

onMounted(()=>{
  if(props.draggable){
      rowDraggable();
  }
})

</script>
