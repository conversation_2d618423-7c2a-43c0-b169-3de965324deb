<template>
  <div class="container">
    <div class="tit-div">
      <span class="title">规则引擎</span>
    </div>
    <div class="options-div">
      <!-- <span :class="{ active: currentOption === 'rule' }" class="op-rule" @click="handleOptions('rule')">规则</span> -->
      <span :class="{ active: currentOption === 'project' }" class="op-rule" @click="handleOptions('project')">项目</span>
      <span class="line"></span>
      <span :class="{ active: currentOption === 'model' }" class="op-rule" @click="handleOptions('model')">模型</span>
      <span class="line"></span>
      <span :class="{ active: currentOption === 'method' }" class="op-rule" @click="handleOptions('method')">方法</span>
      <span class="line"></span>
      <span :class="{ active: currentOption === 'dictionary' }" class="op-rule" @click="handleOptions('dictionary')">数据字典</span>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { useRouter,useRoute } from 'vue-router'
const router = useRouter()
const route = useRoute()
const currentOption = ref('') 
currentOption.value = route.name

// const emit = defineEmits(['optionChange'])
const handleOptions = option => {
  currentOption.value = option
  // emit('optionChange', option)
  router.push({
    name: currentOption.value
  })
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  padding: 10px;
  .tit-div {
    font-size: 18px;
    font-weight: 600;
    width: 180px;
  }
  .options-div {
    flex: 1;
    display: flex;
    align-items: center;
    .op-rule {
      font-size: 14px;
      font-weight: 600;
      display: inline-block;
      padding: 5px 10px;
      cursor: pointer;
    }
    .line {
      display: inline-block;
      width: 1px;
      height: 20px;
      background-color: #c6c6c6;
      margin: 0 10px;
    }
  }
}
.active {
  color: #f6a123;
}
</style>
