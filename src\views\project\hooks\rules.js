import { ref } from "vue";

export default function () {
  const rules = ref([]);

  function getRules(taskId) {
    // 1.根据任务ID获取规则集
    // 请求规则集
    // 2.拿到规则集
    rules.value = [
      { id: '1',
        rule_name: "规则一",
        condition_groups: [
          {
            relation: "",
            conditions: [
              {
                relation: "",
                source_type: "0",
                source_type_name: "参数",
                source_data: "1_1_2",
                source_data_name: "医疗数据/就诊记录/就诊时间",
                source_data_type: "string",
                operator: "1",
                operator: "等于",
                target_type: "3",
                target_type_name: "固定值",
                target_data: "2025-8-15",
                target_dataName: "2025-8-15",
              },
              {
                relation: "or",
                source_type: "0",
                source_type_name: "参数",
                source_data: "1_1_2",
                source_data_name: "医疗数据/就诊记录/就诊时间",
                source_data_type: "string",
                operator: "1",
                operator: "等于",
                target_type: "3",
                target_type_name: "固定值",
                target_data: "2025-8-15",
                target_dataName: "2025-8-15",
              },
            ],
          },
          {
            relation: "or",
            conditions: [
              {
                relation: "",
                source_type: "0",
                source_type_name: "参数",
                source_data: "1_1_2",
                source_data_name: "医疗数据/就诊记录/就诊时间",
                source_data_type: "string",
                operator: "1",
                operator: "等于",
                target_type: "3",
                target_type_name: "固定值",
                target_data: "2025-8-15",
                target_dataName: "2025-8-15",
              },
              {
                relation: "or",
                source_type: "0",
                source_type_name: "参数",
                source_data: "1_1_2",
                source_data_name: "医疗数据/就诊记录/就诊时间",
                source_data_type: "string",
                operator: "1",
                operator: "等于",
                target_type: "3",
                target_type_name: "固定值",
                target_data: "2025-8-15",
                target_dataName: "2025-8-15",
              },
            ],
          },
        ],
        results: {
          conclusion: "不通过",
          terminate_process: true,
          description: "在此区域外就医的医疗费用不属于保险责任范围，不赔"
        }
      },
    ];
  }

  return {rules, getRules}
}
