<template>
  <div class="sidebar-container">
    <div class="title-div">
      <div class="title">
        <span>{{ title }}列表</span>
        <el-icon v-if="operate.includes('addGroup')" :size="12" @click="handleAdd"><Plus /></el-icon>
      </div>
      <div class="input">
        <el-input v-model="inputValue" :prefix-icon="Search" placeholder="搜索" class="input-class" clearable />
      </div>
    </div>

    <div class="content">
      <el-scrollbar>
        <div
          class="item-group"
          v-for="(group, gIndex) in filteredGroups"
          :key="gIndex"
          :class="{
            'item-group-hidden': groupHidden[gIndex],
            'item-group-active': group.id === activeIndex || group.list?.some(item => item.id === activeIndex)
          }"
        >
          <!-- 有 list 的分组 -->
          <template v-if="group.list && group.list.length">
            <div class="item-group-title">
              <div class="group-title-box" @click="handleClickGroup(gIndex)">
                <el-icon :size="14">
                  <component :is="groupHidden[gIndex] ? 'ArrowRight' : 'ArrowDown'" />
                </el-icon>
                <span class="item-group-text">{{ group.title }}</span>
              </div>
              <div class="group-btn-view">
                <el-tooltip v-if="operate.includes('add')" class="box-item" effect="dark" content="新建项目" placement="top">
                  <el-icon style="margin-right: 8px" :size="10" @click="handleAddItem(group)"><Plus /></el-icon>
                </el-tooltip>
                <el-popover
                  v-if="operate.includes('edit') || operate.includes('delete')"
                  class="box-item"
                  placement="right-start"
                  trigger="hover"
                  popper-style="padding: 5px  10px;"
                >
                  <template #reference>
                    <el-icon :size="10"><MoreFilled /></el-icon>
                  </template>
                  <div class="pop-content">
                    <div class="pop-item" v-if="operate.includes('edit')" @click="handlePutItemName(group)">
                      <el-icon :size="14"><Edit /></el-icon>
                      <span>编辑</span>
                    </div>
                    <div class="pop-item" v-if="operate.includes('delete')" @click="handleDeleteItem(group)">
                      <el-icon :size="14"><Delete /></el-icon>
                      <span>删除</span>
                    </div>
                  </div>
                </el-popover>
              </div>
            </div>

            <!-- 项目列表 -->
            <div v-for="(item, iIndex) in group.list" :key="iIndex" class="item" :class="{ active: activeIndex === item.id }">
              <span class="item-text" @click="handleClickItem(item)">{{ item.title }}</span>
              <el-popover
                v-if="operate.includes('edit') || operate.includes('delete')"
                class="box-item"
                placement="right-start"
                trigger="hover"
                popper-style="padding: 5px  10px;"
              >
                <template #reference>
                  <el-icon :size="10"><MoreFilled /></el-icon>
                </template>
                <div class="pop-content">
                  <div class="pop-item" v-if="operate.includes('edit')" @click="handlePutItemName(item)">
                    <el-icon :size="14"><Edit /></el-icon>
                    <span>编辑</span>
                  </div>
                  <div class="pop-item" v-if="operate.includes('delete')" @click="handleDeleteItem(item)">
                    <el-icon :size="14"><Delete /></el-icon>
                    <span>删除</span>
                  </div>
                </div>
              </el-popover>
            </div>
          </template>

          <!-- 没有 list，直接单项 -->
          <template v-else>
            <div class="item single-item" :class="{ active: activeIndex === group.id }" @click="handleClickItem(group)">
              <span class="item-text">{{ group.title }}</span>
            </div>
          </template>
        </div>
      </el-scrollbar>
    </div>

  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'

// const router = useRouter()
// const route = useRoute()
// const currentOption = ref('')
// currentOption.value = route.name
// const ruleObj = {
//   project: '项目',
//   model: '模型',
//   method: '方法',
//   dictionary: '数据字典'
// }

const props = defineProps({
  title: { type: String, default: '' },
  operate: { type: Array, default: () => [] },
  groups: { type: Array, default: () => [] }
})

const emit = defineEmits(['choise', 'addGroup', 'add', 'edit', 'delete'])

const inputValue = ref('')
const activeIndex = ref(null)
const groupHidden = ref([])

// 监听 groups 初始化折叠状态
watch(
  () => props.groups,
  val => {
    groupHidden.value = val.map(() => false)

    // 默认激活第一个菜单
    if (val.length > 0) {
      const firstGroup = val[0]
      if (firstGroup.list && firstGroup.list.length > 0) {
        activeIndex.value = firstGroup.list[0].id
        emit('choise', { item: firstGroup.list[0] })
      } else {
        activeIndex.value = firstGroup.id
        emit('choise', { item: firstGroup })
      }
    }
  },
  { immediate: true, deep: true }
)

// 搜索过滤
const filteredGroups = computed(() => {
  if (!inputValue.value) return props.groups
  return props.groups
    .map(group => {
      if (group.list && group.list.length) {
        return {
          ...group,
          list: group.list.filter(item => item.title.includes(inputValue.value))
        }
      }
      return group.title.includes(inputValue.value) ? group : null
    })
    .filter(Boolean)
})

// 点击项
const handleClickItem = item => {
  activeIndex.value = item.id
  emit('choise', { item: item })
}
// 点击组
const handleClickGroup = index => {
  groupHidden.value[index] = !groupHidden.value[index]
}

// 新建项目
const handleAddItem = group => {
  emit('add', group)
}
// 新建项目组
const handleAdd = () => {
  emit('addGroup')
}
//编辑
const handlePutItemName = item => {
  emit('edit', item)
}
// shanchu
const handleDeleteItem = item => {
  emit('delete', item)
}
</script>

<style scoped lang="scss">
.sidebar-container {
  width: 250px;
  height: calc(100vh - 50px);
  border-right: solid 2px #e0e0e0;

  .title-div {
    padding: 10px;
    .title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .content {
    height: calc(100vh - 140px);

    .item-group {
      max-height: 300px;
      overflow: hidden;
      transition: max-height 0.3s linear;
      padding: 0 8px;

      &.item-group-active .item-group-title {
        color: #f6a123;
      }

      .item-group-title {
        font-size: 14px;
        color: #09090b;
        padding: 8px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 10px;
        cursor: pointer;

        .group-title-box {
          display: flex;
          align-items: center;
          flex: 1;
        }
        .group-btn-view {
          display: flex;
          align-items: center;
        }
        &:hover {
          background: #f4f4f5;
        }
      }

      .item {
        color: #09090b;
        padding: 8px 10px 8px 26px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 10px;
        margin: 5px 0;

        .item-text {
          width: 90%;
          cursor: pointer;
        }
        &:hover {
          background: #f4f4f5;
        }
      }

      .single-item {
        padding: 8px 10px;
        cursor: pointer;
        &:hover {
          background: #f4f4f5;
        }
      }
    }

    .item-group-hidden {
      max-height: 40px;
    }

    .active {
      background-color: #f6a12326 !important;
      color: #f6a123 !important;
    }
  }
}

.input-class {
  font-size: 14px;
}
.el-icon {
  cursor: pointer;
}
.pop-content {
  display: flex;
  flex-direction: column;
  .pop-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    height: 24px;
    cursor: pointer;
    span {
      margin-left: 10px;
    }
    &:hover {
      background: #fef6e8;
    }
  }
}
</style>
