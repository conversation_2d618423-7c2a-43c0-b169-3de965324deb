<template>
  <div class="sidebar-container">
    <div class="title-div">
      <div class="title">
        <span>{{ props.title }}列表</span>
        <el-icon v-if="props.type == 'project'" :size="12" @click="handleAdd"><Plus /></el-icon>
      </div>
      <div class="input">
        <el-input v-model="inputValue" :prefix-icon="Search" placeholder="搜索" class="input-class" clearable @change="getDataList" />
      </div>
    </div>

    <div class="content" v-loading="loading">
      <el-scrollbar>
        <div
          class="item-group"
          v-for="(group, gIndex) in groups"
          :key="gIndex"
          :class="{
            'item-group-hidden': groupHidden[gIndex],
            'item-group-active': group.id === activeIndex || group.list?.some(item => item.id === activeIndex)
          }"
        >
          <!-- 有 list 的分组 -->
          <template v-if="group.list">
            <div class="item-group-title">
              <div class="group-title-box" @click="handleClickGroup(gIndex)">
                <el-icon :size="14">
                  <component :is="groupHidden[gIndex] ? 'ArrowRight' : 'ArrowDown'" />
                </el-icon>
                <span class="item-group-text">{{ group.name }}</span>
              </div>
              <div class="group-btn-view">
                <el-tooltip v-if="props.type == 'project'" class="box-item" effect="dark" content="新建项目" placement="top">
                  <el-icon style="margin-right: 8px" :size="10" @click="handleAddItem(group)"><Plus /></el-icon>
                </el-tooltip>
                <el-popover
                  v-if="props.type == 'project'"
                  class="box-item"
                  placement="right-start"
                  trigger="hover"
                  popper-style="padding: 5px  10px;"
                >
                  <template #reference>
                    <el-icon :size="10"><MoreFilled /></el-icon>
                  </template>
                  <div class="pop-content">
                    <div class="pop-item" v-if="props.type == 'project'" @click="handlePutGrouopName(group)">
                      <el-icon :size="14"><Edit /></el-icon>
                      <span>编辑</span>
                    </div>
                    <div class="pop-item" v-if="props.type == 'project'" @click="handleDeleteGroup(group)">
                      <el-icon :size="14"><Delete /></el-icon>
                      <span>删除</span>
                    </div>
                  </div>
                </el-popover>
              </div>
            </div>

            <!-- 项目列表 -->
            <div v-for="(item, iIndex) in group.list" :key="iIndex" class="item" :class="{ active: activeIndex === item.id }">
              <span class="item-text" @click="handleClickItem(item)">{{ item.name }}</span>
              <el-popover
                v-if="props.type == 'project'"
                class="box-item"
                placement="right-start"
                trigger="hover"
                popper-style="padding: 5px  10px;"
              >
                <template #reference>
                  <el-icon :size="10"><MoreFilled /></el-icon>
                </template>
                <div class="pop-content">
                  <div class="pop-item" v-if="props.type == 'project'" @click="handlePutItemName(item)">
                    <el-icon :size="14"><Edit /></el-icon>
                    <span>编辑</span>
                  </div>
                  <div class="pop-item" v-if="props.type == 'project'" @click="handleDeleteItem(item)">
                    <el-icon :size="14"><Delete /></el-icon>
                    <span>删除</span>
                  </div>
                </div>
              </el-popover>
            </div>
          </template>

          <!-- 没有 list，直接单项 -->
          <template v-else>
            <div class="item single-item" :class="{ active: activeIndex === group.id }" @click="handleClickItem(group)">
              <span class="item-text">{{ group.name }}</span>
            </div>
          </template>
        </div>
      </el-scrollbar>
    </div>
    <!-- 新建项目组弹窗 -->
    <el-dialog :title="groupEdit ? '编辑项目组' : '新建项目组'" v-model="addGroupDialogVisible" width="30%" @close="handleCloseGroup">
      <el-input v-model="groupName" placeholder="请输入名称" clearable />
      <template #footer>
        <span>
          <el-button @click="addGroupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAddGroup">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 新建项目弹窗 -->
    <el-dialog title="新建项目" v-model="addGroupItemDialogVisible" width="30%" @close="handleCloseGroupItem">
      <el-input v-model="groupItemTitle" placeholder="请输入名称" clearable />
      <template #footer>
        <span>
          <el-button @click="addGroupItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAddGroupItem">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// const ruleObj = {
//   project: '项目',
//   model: '模型',
//   method: '方法',
//   dictionary: '数据字典'
// }
import { queryProjectGroupList } from '@/api/project'
import { queryModelLibraryList } from '@/api/model'
// const route = useRoute()
// const currentOption = ref('')
// currentOption.value = route.name

const groups = ref([])

const loading = ref(false)
const inputValue = ref('')
const activeIndex = ref(null)
const groupHidden = ref([])

const props = defineProps({
  title: { type: String, default: '' },
  type: { type: String, default: '' }
  // operate: { type: Array, default: () => [] },
  // groups: { type: Array, default: () => [] }
})

watch(
  () => props.type,
  val => {
    let name = val
    loading.value = true
    if (name == 'project') {
      getList()
      return
    }
    if (name == 'model') {
      getModelList()
      return
    }
  }
)
//获取项目 列表
const getList = () => {
  const params = {
    name: inputValue.value
  }
  queryProjectGroupList(params).then(res => {
    groups.value = res.data
    loading.value = false
  })
}
// 获取模型 列表
const getModelList = () => {
  const params = {
    modelLibraryName: inputValue.value,
    projectId: 0
  }
  queryModelLibraryList(params).then(res => {
    groups.value = res.data
    loading.value = false
  })
}
// 初始化 列表
const getDataList = () => {
  if (props.type == 'project') {
    getList()
  }
  if (props.type == 'model') {
    getModelList()
  }
}
getDataList()
const emit = defineEmits(['choise'])

// 监听 groups 初始化折叠状态
watch(
  () => groups,
  val => {
    groupHidden.value = val.value.map(() => false)

    // 默认激活第一个菜单
    if (val.value.length > 0) {
      const firstGroup = val.value[0]
      if (firstGroup.list && firstGroup.list.length > 0) {
        activeIndex.value = firstGroup.list[0].id
        emit('choise', firstGroup.list[0])
      } else {
        activeIndex.value = firstGroup.id
        emit('choise', firstGroup)
      }
    }
  },
  { immediate: true, deep: true }
)

// // 搜索过滤
// const filteredGroups = computed(() => {
//   if (!inputValue.value) return groups.value
//   getDataList()
//   // return groups.value.map(group => {
//   //     if (group.list && group.list.length) {
//   //       return {
//   //         ...group,
//   //         list: group.list.filter(item => item.name.includes(inputValue.value))
//   //       }
//   //     }
//   //     return group.name.includes(inputValue.value) ? group : null
//   //   })
//   //   .filter(Boolean)
// })

// 点击项
const handleClickItem = item => {
  activeIndex.value = item.id
  emit('choise', item)
}
// 点击组
const handleClickGroup = index => {
  groupHidden.value[index] = !groupHidden.value[index]
}

import { addProjectGroup, addProject,updateProject,deleteProject, updateProjectGroup ,deleteProjectGroup} from '@/api/project'
// 新建项目组
const groupName = ref('')
const groupEdit = ref(false)
const addGroupDialogVisible = ref(false)
const groupId = ref(0)
const handleAdd = () => {
  addGroupDialogVisible.value = true
}
const handleConfirmAddGroup = () => {
  if (!groupEdit.value) {
    const data = {
      name: groupName.value,
      createUser: 0
    }

    addProjectGroup(data).then(res => {
      if (res.code === 200) {
        getDataList()
        addGroupDialogVisible.value = false
      }
    })
  }
  if (groupEdit.value) {
    const data = {
      name: groupName.value,
      id: groupId.value
    }
    updateProjectGroup(data).then(res => {
      if (res.code === 200) {
        getDataList()
        addGroupDialogVisible.value = false
      }
    })
  }
}
// 编辑项目组
const handlePutGrouopName = group => {
  groupEdit.value = true
  addGroupDialogVisible.value = true
  groupName.value = group.name
  groupId.value = group.id
}
const handleCloseGroup = () => {
  groupName.value = ''
  groupEdit.value = false
  groupId.value = 0
}

// 删除项目组
const handleDeleteGroup = group => {
  ElMessageBox.confirm('确认删除此项目组?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(()=>{
    deleteProjectGroup({id:group.id}).then(res=>{
      if(res.code===200){
        ElMessage({
        type: 'success',
        message: '删除完成',
      })
        getDataList()
      }
    })
  })
}
// 新建项目
const addGroupItemDialogVisible = ref(false)
const groupItemTitle = ref('')
const projectGroupId = ref(0)
const itemEdit = ref(false)
const itemId = ref(0)
const handleAddItem = group => {
  projectGroupId.value = group.id
  addGroupItemDialogVisible.value = true
}
const handleConfirmAddGroupItem = () => {
  if(!itemEdit.value){
    const data = {
      name: groupItemTitle.value,
      nameZh: groupItemTitle.value,
      projectGroupId: projectGroupId.value
    }
    addProject(data).then(res => {
      if (res.code === 200) {
        getDataList()
        groupItemTitle.value = ''
        projectGroupId.value = 0
        addGroupItemDialogVisible.value = false
      }
    })
  }
  if(itemEdit.value){
    const data = {
      id:itemId.value,
      name: groupItemTitle.value,
    }
    updateProject(data).then(res=>{
      if(res.code===200){
        getDataList()
        addGroupItemDialogVisible.value = false
      }
    })
  
  }
}
//编辑项目
const handlePutItemName = item => {
  itemEdit.value = true
  itemId.value = item.id
  groupItemTitle.value = item.name
  addGroupItemDialogVisible.value = true
}
const handleCloseGroupItem = () => {
  groupItemTitle.value = ''
  projectGroupId.value = 0
  itemEdit.value = false
  itemId.value = 0
}
// 删除项目
const handleDeleteItem = item => {
  ElMessageBox.confirm('确认删除此项目?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(()=>{
    deleteProject({id:item.id}).then(res=>{
      if(res.code===200){
        ElMessage({
        type: 'success',
        message: '删除完成',
      })
        getDataList()
      }
    })
  })
}
</script>

<style scoped lang="scss">
.sidebar-container {
  width: 250px;
  height: calc(100vh - 50px);
  border-right: solid 2px #e0e0e0;

  .title-div {
    padding: 10px;
    .title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .content {
    height: calc(100vh - 140px);

    .item-group {
      max-height: 300px;
      overflow: hidden;
      transition: max-height 0.3s linear;
      padding: 0 8px;

      &.item-group-active .item-group-title {
        color: #f6a123;
      }

      .item-group-title {
        font-size: 14px;
        color: #09090b;
        padding: 8px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 10px;
        height: 36px;
        cursor: pointer;

        .group-title-box {
          display: flex;
          align-items: center;
          flex: 1;
        }
        .group-btn-view {
          display: flex;
          align-items: center;
        }
        &:hover {
          background: #f4f4f5;
        }
      }

      .item {
        color: #09090b;
        padding: 8px 10px 8px 26px;
        font-size: 14px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 10px;
        margin: 5px 0;
        height: 36px;
        .item-text {
          width: 90%;
          cursor: pointer;
        }
        &:hover {
          background: #f4f4f5;
        }
      }

      .single-item {
        padding: 8px 10px;
        cursor: pointer;
        &:hover {
          background: #f4f4f5;
        }
      }
    }

    .item-group-hidden {
      max-height: 40px;
    }

    .active {
      background-color: #f6a12326 !important;
      color: #f6a123 !important;
    }
  }
}

.input-class {
  font-size: 14px;
}
.el-icon {
  cursor: pointer;
}
.pop-content {
  display: flex;
  flex-direction: column;
  .pop-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    height: 24px;
    cursor: pointer;
    span {
      margin-left: 10px;
    }
    &:hover {
      background: #fef6e8;
    }
  }
}
</style>
