<template>
  <div class="taskCard collapse-card">
    <el-collapse v-model="activePanel">
      <el-collapse-item name="task">
        <template #title>
          <div class="cardheader" @click.stop="() => {}">
            <h2 class="header_title">任务</h2>
            <div class="header_action">
              <el-button link type="primary" @click="handleAdd">新增</el-button>
              <el-button link type="primary" @click="handleImport"
                >导入</el-button
              >
            </div>
          </div>
        </template>
        <CustomTable
          draggable
          class="table-container"
          :tableData="taskData"
          :columns="taskColumns"
          @draggable-change="updateTaskTable"
        >
          <template #action="{ row }">
            <el-popover
              class="box-item"
              placement="right-start"
              trigger="hover"
              popper-style="padding: 5px  10px;"
            >
              <template #reference>
                <el-icon :size="13"><MoreFilled /></el-icon>
              </template>
              <div class="pop-content">
                <div class="pop-item" @click="handleDownAdd(row)">
                  <el-icon :size="14"><Bottom /></el-icon>
                  <span>向下新增</span>
                </div>
                <div class="pop-item" @click="handleEdit">
                  <el-icon :size="14"><Edit /></el-icon>
                  <span>重命名</span>
                </div>
                <div class="pop-item" @click="handleDelete">
                  <el-icon :size="14"><Delete /></el-icon>
                  <span>删除</span>
                </div>
              </div>
            </el-popover>
          </template>
        </CustomTable>
      </el-collapse-item>
    </el-collapse>
    <el-dialog
      v-model="taskDialogVisible"
      :title="title"
      width="30%"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">{{ title }}</span>
        </div>
      </template>
      <el-form ref="formRef" :model="formData" label-width="80px">
        <el-form-item label="任务名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入任务名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  projectId: {
    type: [Number, String],
    required: true
  },
  initData:{
    type: Array,
    default: () => []
  }
})

const activePanel = ref("task");

const taskColumns = ref([
  { label: "序号", type: "index", width: "70", align: "left" },
  { label: "任务名称", prop: "nameZh", align: "left" },
  { label: "", slot: "action", width: "40", align: "center", fixed: "right" },
]);

const taskData = ref([...props.initData]);

const formRef = ref(null);
const title = ref("新增任务");
const taskDialogVisible = ref(false);
const formData = ref({});


watch(
  () => props.initData,
  newVal => {
    taskData.value = [...newVal];
  },
  { deep: true }
)

const handleAdd = () => {
  resetForm();
  title.value = "新增任务";
  taskDialogVisible.value = true;
};

const handleEdit = () => {
  resetForm();
  title.value = "编辑任务";
  taskDialogVisible.value = true;
};

const handleDownAdd = (row) => {
  const index = taskData.value.findIndex((item) => item.id === row.id);
  taskData.value.splice(index + 1, 0, {
    id: taskData.value.length + 1,
    name: "任务" + (taskData.value.length + 1),
  });
  proxy.$modal.msgSuccess("新增成功");
};

const resetForm = () => {
  formData.value = {
    name: "",
  };
  formRef.value?.resetFields();
};

const handleDelete = (row) => {
  proxy.$modal.confirm("是否确认删除此任务及相关规则？").then(() => {
    taskData.value = [];
    proxy.$modal.msgSuccess("删除成功");
  });
};

const cancel = () => {
  taskDialogVisible.value = false;
  resetForm();
};
const submitForm = () => {
  proxy.$modal.confirm("是否确认提交数据？").then(() => {
    taskData.value.push({
      id: taskData.value.length + 1,
      name: "任务" + taskData.value.length + 1,
    });
    proxy.$modal.msgSuccess("新增成功");
    taskDialogVisible.value = false;
    resetForm();
  });
};

const handleImport = () => {
  proxy.$modal.msgWarning("导入任务功能尚未开发");
};

const updateTaskTable = (newIndex, oldIndex) => {
  const movedItem = taskData.value.splice(oldIndex, 1)[0];
  taskData.value.splice(newIndex, 0, movedItem);
  proxy.$modal.msgSuccess("排序成功");
};
</script>
<style lang="scss" scoped>
.taskCard {
  background-color: #fff;
  padding: 0px 10px;
  .el-collapse {
    --el-collapse-border-color: transparent;
  }
  .cardheader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header_title {
      color: #09090B;
      font-size: 16px;
    }
    .header_action {
      margin-right: 10px;
    }
  }
  .table-container {
    :deep(.cell) {
      padding: 0px;
    }
    .el-button {
      margin: 0px;
    }
  }
}

.pop-content {
  display: flex;
  flex-direction: column;
  .pop-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    height: 24px;
    cursor: pointer;
    span {
      margin-left: 10px;
    }
    &:hover {
      background: #fef6e8;
    }
  }
}
.el-dialog {
  .dialog-header {
    border-left: 2px solid #f6a123;
    padding-left: 10px;
    .dialog-title {
      color: #f6a123;
    }
  }
  .el-form {
    width: 80%;
    margin: 10px auto;
  }
}
</style>
