<template>
  <div class="taskCard">
    <el-collapse v-model="activePanel">
      <el-collapse-item name="task">
        <template #title>
            <div class="cardheader" @click.stop="() => {}">
                 <h2 class="header_title">任务</h2> 
                 <div class="header_action">
                    <el-button   link type="warning" @click="handleAdd">新增</el-button>
                    <el-button   link type="warning" @click="handleImport">导入</el-button>
                 </div>
            </div>
        </template>
        <CustomTable draggable  class="table-container" :tableData="modelData" :columns="modelColumns">
          <template #action="{row}">
            <el-popover class="box-item" placement="right-start" trigger="hover" popper-style="padding: 5px  10px;">
                  <template #reference>
                    <el-icon :size="13"><MoreFilled /></el-icon>
                  </template>
                  <div class="pop-content">
                    <div class="pop-item" @click="handleAdd">
                      <el-icon :size="14"><Edit /></el-icon>
                      <span>重命名</span>
                    </div>
                    <div class="pop-item" @click="handleAdd">
                      <el-icon :size="14"><Delete /></el-icon>
                      <span>删除</span>
                    </div>
                  </div>
                </el-popover>
          </template>
        </CustomTable>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
const activePanel = ref("task");

const modelColumns = ref([
    { label: "序号", type:'index', width:'100', align: "center" },
    { label: "模型名称", prop: "name",  align: "left" },
    { label:"" ,  slot:'action' ,  width:'40',  align:'center', fixed: "right" }
])

const modelData = ref([
    { id:1,name: "我是任务名称1" },
    { id:2,name: "我是任务名称2" },

])

const handleAdd = () => {
  console.log("新增模型");
};
const handleImport = () => {
  console.log("导入模型");
};
</script>
<style lang="scss" scoped>
.taskCard {
  background-color: #fff;
  padding: 0px 10px;  
  .el-collapse{
    --el-collapse-border-color: transparent;
  }
  .cardheader{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header_title {
      font-size: 18px;
    }
    .header_action {
      margin-right: 10px;
    }
  }
  .table-container{
    :deep(.cell){
        padding: 0px;
    }
    .el-button{
      margin: 0px;
    }
  }
}

.pop-content {
  display: flex;
  flex-direction: column;
  .pop-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    height: 24px;
    cursor: pointer;
    span {
      margin-left: 10px;
    }
    &:hover {
      background: #fef6e8;
    }
  }
}
</style>
