<template>
  <div class="container">
    <RSideBar :groups="groups"></RSideBar>
    <div class="main-content">
      <div class="model-content">
        <CustomTable class="table-container" :loading="loading" :tableData="dictData" :columns="dictColumns"></CustomTable>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import RSideBar from "@/components/RSideBar.vue";
import CustomTable from "@/components/CustomTable/index.vue";

const props = defineProps({
  modelId: {
    type: Number,
    default:1,
    required: true,
  },
})

const groups = ref([
  {
    title: "模型1",
    id: 1,
    list: [
      {
        title: "模型1-1",
        id: 11,
      },
      {
        title: "模型1-2",
        id: 21,
      },
    ],
  },
  {
    title: "模型2",
    id: 2,
    list: [
      {
        title: "模型2-1",
        id: 12,
      },
      {
        title: "模型2-2",
        id: 22,
      },
    ],
  },
]);
const loading = ref(true);

const dictColumns = ref([
  {
    label: "字典标签",
    prop: "key",
  },
  {
    label: "字典键值",
    prop: "value",
  },
  {
    label: "描述说明",
    prop: "desc",
  },
])
const dictData = ref([
  {
    key: "居名身份证",
    value: "3",
    desc: "用户的身份证号码，最长为18位数字和字母组合",
  },
  {
    key: "居名户口簿",
    value: "4",
    desc: "用户的户口簿号码，最长为18位数字和字母组合",
  },
  {
    key: "护照",
    value: "5",
    desc: "用户的护照号码，最长为18位数字和字母组合",
  },
]);

const getDictData = () => {
  loading.value = true;
  setTimeout(() => {
    dictData.value = [...dictData.value];
    loading.value = false;
  }, 1000);
};

watch(
  () => props.modelId,
  (newVal) => {
    getDictData();
  },
  {
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
.model-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .table-container {
    flex: 1;
  }
}
</style>
