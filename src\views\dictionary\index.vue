<template>
  <div class="model-content">
    <CustomTable
      class="table-container"
      :loading="loading"
      :tableData="dictData"
      :columns="dictColumns"
    ></CustomTable>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
import { getDictDetail } from "@/api/dictionary/index";

const props = defineProps({
  itemId: {
    type: [Number, String],
    required: true,
  },
});

const loading = ref(false);

const dictColumns = ref([
  {
    label: "字典标签",
    prop: "nameZh",
  },
  {
    label: "字典键值",
    prop: "name",
  },
  {
    label: "描述说明",
    prop: "value",
  },
]);
const dictData = ref([]);

const getDictData = (dictId) => {
  loading.value = true;
  getDictDetail(dictId)
    .then((res) => {
      dictData.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
};

watch(
  () => props.itemId,
  (newVal) => {
    if (newVal) {
      getDictData(newVal);
    }
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped>
.model-content {
  width: 100%;
  min-height: calc(100vh - 74px);
  padding: 20px;
  border-radius: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .table-container {
    flex: 1;
  }
}
</style>
