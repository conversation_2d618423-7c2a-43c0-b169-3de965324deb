<template>
  <div class="processCard">
    <el-collapse v-model="activePanel">
      <el-collapse-item name="process">
        <template #title>
            <div class="cardheader" @click.stop="() => {}">
                 <h2 class="header_title" >数据预处理</h2> 
                 <div class="header_action">
                    <el-button  link type="warning" @click="handleAdd">新增</el-button>
                 </div>
            </div>
        </template>
        <CustomTable  class="table-container" :tableData="processData" :columns="processColumns">
          <template #action="{row}">
            <el-popover class="box-item" placement="right-start" trigger="hover" popper-style="padding: 5px  10px;">
                  <template #reference>
                    <el-icon :size="13"><MoreFilled /></el-icon>
                  </template>
                  <div class="pop-content">
                    <div class="pop-item" @click="handleEdit">
                      <el-icon :size="14"><Edit /></el-icon>
                      <span>编辑</span>
                    </div>
                    <div class="pop-item" @click="handleResult">
                      <el-icon :size="14"><Finished /></el-icon>
                      <span>查看输出</span>
                    </div>
                    <div class="pop-item" @click="handleDelete">
                      <el-icon :size="14"><Delete /></el-icon>
                      <span>删除</span>
                    </div>
                  </div>
                </el-popover>
          </template>
        </CustomTable>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
const activePanel = ref("process");

const processColumns = ref([
    { label: "方法名称", prop: "name", align: "left" },
    { label:"" ,  slot:'action' ,  width:'40',  align:'center', fixed: "right" }
])

const processData = ref([
    { name: "模型1" },
])

const handleEdit = () => {
  console.log("新增预处理流程");
};
const handleDelete = () => {
  console.log("删除预处理流程");
};
const handleResult = () => {
  console.log("查看输出结果");
};
const handleAdd = () => {
  console.log("新增预处理流程");
};
</script>
<style lang="scss" scoped>
.processCard {
  background-color: #fff;
  padding: 0px 10px;  
  margin-bottom: 20px;
  .el-collapse{
    --el-collapse-border-color: transparent;
  }
  .cardheader{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header_title {
      font-size: 18px;
    }
    .header_action {
      margin-right: 10px;
    }
  }
  .table-container{
    :deep(.cell){
        padding: 0px;
    }
  }
  
}
.pop-content {
  display: flex;
  flex-direction: column;
  .pop-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    height: 24px;
    cursor: pointer;
    span {
      margin-left: 10px;
    }
    &:hover {
      background: #fef6e8;
    }
  }
}
</style>
