<template>
  <div class="processCard collapse-card">
    <el-collapse v-model="activePanel">
      <el-collapse-item name="process">
        <template #title>
          <div class="cardheader" @click.stop="() => {}">
            <h2 class="header_title">数据预处理</h2>
            <div class="header_action">
              <el-button link type="primary" @click="handleAdd">新增</el-button>
            </div>
          </div>
        </template>
        <CustomTable
          class="table-container"
          :tableData="processData"
          :columns="processColumns"
        >
          <template #action="{ row }">
            <el-popover
              class="box-item"
              placement="right-start"
              trigger="hover"
              popper-style="padding: 5px  10px;"
            >
              <template #reference>
                <el-icon :size="13"><MoreFilled /></el-icon>
              </template>
              <div class="pop-content">
                <div class="pop-item" @click="handleEdit">
                  <el-icon :size="14"><Edit /></el-icon>
                  <span>编辑</span>
                </div>
                <div class="pop-item" @click="handleResult">
                  <el-icon :size="14"><Finished /></el-icon>
                  <span>查看输出</span>
                </div>
                <div class="pop-item" @click="handleDelete">
                  <el-icon :size="14"><Delete /></el-icon>
                  <span>删除</span>
                </div>
              </div>
            </el-popover>
          </template>
        </CustomTable>
      </el-collapse-item>
    </el-collapse>
    <el-dialog
      v-model="processDialogVisible"
      :title="title"
      width="30%"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">{{ title }}</span>
        </div>
      </template>
      <el-form ref="formRef" :model="formData" label-width="80px">
        <el-form-item label="简称" prop="shortName">
          <el-input v-model="formData.shortName" placeholder="请输入简称" />
        </el-form-item>
        <el-form-item label="方法" prop="method">
          <el-cascader
            style="width: 100%"
            v-model="formData.method"
            :options="methodOptions"
            placeholder="请选择方法"
            clearable
          />
        </el-form-item>
        <el-form-item label="参数" prop="params">
          <el-select v-model="formData.params" placeholder="请选择参数">
            <el-option
              v-for="item in paramOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
const { proxy } = getCurrentInstance();


const props = defineProps({
  projectId: {
    type: [Number, String],
    required: true
  },
  initData:{
    type: Array,
    default: () => []
  }
})



const activePanel = ref("process");
const processColumns = ref([
  { label: "方法名称", prop: "nameZh", align: "left" },
  { label: "", slot: "action", width: "40", align: "center", fixed: "right" },
]);

const processData = ref([...props.initData]);

const formRef = ref(null);
const title = ref("新增数据预处理");
const processDialogVisible = ref(false);
const formData = ref({});
const methodOptions = ref([
  {
    value: "0",
    label: "OCR",
    children: [
      { value: "1", label: "识别身份证-人像面" },
      { value: "2", label: "识别身份证-国徽面" },
    ],
  },
]);
const paramOptions = ref([
  {
    label: "参数1",
    value: "1",
  },
  {
    label: "参数2",
    value: "2",
  },
]);


watch(
  () => props.initData,
  newVal => {
    processData.value = [...newVal];
  },
  { deep: true }
)

const handleEdit = () => {
  resetForm();
  title.value = "编辑数据预处理";
  processDialogVisible.value = true;
};
const handleDelete = () => {
  proxy.$modal.confirm("是否确认删除该方法？").then(() => {
    proxy.$modal.msgSuccess("删除成功");
  });
};
const handleResult = () => {
  console.log("查看输出结果");
};
const handleAdd = () => {
  resetForm();
  title.value = "新增数据预处理";
  processDialogVisible.value = true;
};
const resetForm = () => {
  formData.value = {
    shortName: "",
    method: "",
    params: "",
  };
  formRef.value?.resetFields();
};

const cancel = () => {
  processDialogVisible.value = false;
  resetForm();
};

const submitForm = () => {
  proxy.$modal.confirm("是否确认提交数据？").then(() => {
    processDialogVisible.value = false;
    resetForm();
    proxy.$modal.msgSuccess("新增成功");
  });
};
</script>
<style lang="scss" scoped>
.processCard {
  background-color: #fff;
  padding: 0px 10px;
  margin-bottom: 20px;
  .el-collapse {
    --el-collapse-border-color: transparent;
  }
  .cardheader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header_title {
      color: #09090B;
      font-size: 16px;
    }
    .header_action {
      margin-right: 10px;
    }
  }
  .table-container {
    :deep(.cell) {
      padding: 0px;
    }
  }
}
.pop-content {
  display: flex;
  flex-direction: column;
  .pop-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    height: 24px;
    cursor: pointer;
    span {
      margin-left: 10px;
    }
    &:hover {
      background: #fef6e8;
    }
  }
}

.el-dialog {
  .dialog-header {
    border-left: 2px solid #f6a123;
    padding-left: 10px;
    .dialog-title {
      color: #f6a123;
    }
  }
  .el-form {
    width: 80%;
    margin: 10px auto;
  }
}
</style>
