import { createApp } from "vue";
import { createPinia } from "pinia";
import "@/assets/styles/index.scss"; // global css
import "@/assets/styles/element/index.scss"; // global css

import App from "./App.vue";
import router from "./router";
import ElementPlus from "element-plus";
// import "element-plus/dist/index.css";
// import 'element-plus/theme-chalk/dark/css-vars.css'

import * as ElementPlusIconsVue from "@element-plus/icons-vue";

// 分页组件
import Pagination from "@/components/Pagination/index.vue";

const app = createApp(App);

app.component("Pagination", Pagination);

app.use(createPinia());
app.use(router);
app.use(ElementPlus);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.mount("#app");
