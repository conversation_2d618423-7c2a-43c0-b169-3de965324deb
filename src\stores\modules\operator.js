import { defineStore } from "pinia";
import { getOperator } from "@/api/operator";

// 操作符
const useOperatorStore = defineStore(
  'operator',
  {
    state: () => ({
      int: [],
      string: [],
      array: [],
      boolean: []
    }),
    actions: {
      // 获取方法
      async getOperator() {
        let result = await getOperator()
        this.int = [
          { id: '1', name: '大于' },
          { id: '2', name: '小于' },
          { id: '3', name: '等于' },
          { id: '4', name: '不等于' }
        ]
        this.string = [
          { id: '1', name: '包含' },
          { id: '2', name: '不包含' },
          { id: '3', name: '等于' },
          { id: '4', name: '不等于' }
        ]
        this.array = [
          { id: '1', name: '包含' },
          { id: '2', name: '不包含' }
        ]
        this.boolean = [
          { id: '1', name: '为真' },
          { id: '2', name: '为假' }
        ]
      }
    }
  }
)

export default useOperatorStore;