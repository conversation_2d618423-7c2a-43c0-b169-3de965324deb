<template>
  <div class="container">
    <RSideBar
      title="项目"
      :groups="groups"
      :operate="['addGroup', 'add', 'edit', 'delete']"
      @choise="choiseSideBar"
      @addGroup="addGroup"
      @add="add"
      @edit="edit"
      @delete="deletebyId"
    ></RSideBar>
    <div class="main-content">
      <el-splitter>
        <el-splitter-panel size="30%" :min="250">
          <div class="leftContent" style="height: calc(100vh - 80px)">
            <el-scrollbar>
              <ModelCard />
              <ProcessCard />
              <TaskCard />
            </el-scrollbar>
          </div>
        </el-splitter-panel>
        <el-splitter-panel :min="400">
          <div class="rightContent">
            <Rule></Rule>
          </div>
        </el-splitter-panel>
      </el-splitter>
    </div>

    <!-- 编辑弹窗 -->
    <!-- <el-dialog title="编辑" v-model="dialogVisible" width="30%">
      <el-input v-model="inputTitle" placeholder="请输入新名称" clearable />
      <template #footer>
        <span>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmRename">确认</el-button>
        </span>
      </template>
    </el-dialog> -->
    <!-- 新建项目组弹窗 -->
    <el-dialog title="新建项目组" v-model="addGroupDialogVisible" width="30%">
      <el-input v-model="groupTitle" placeholder="请输入名称" clearable />
      <template #footer>
        <span>
          <el-button @click="addGroupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAddGroup">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 新建项目弹窗 -->
    <el-dialog title="新建项目" v-model="addGroupItemDialogVisible" width="30%">
      <el-input v-model="groupItemTitle" placeholder="请输入名称" clearable />
      <template #footer>
        <span>
          <el-button @click="addGroupItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAddGroupItem">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import RSideBar from '@/components/RSideBar.vue'
import ModelCard from './component/model.vue'
import ProcessCard from './component/process.vue'
import TaskCard from './component/task.vue'
// import { getGroups } from "@/api/project";
import Rule from "./component/rule.vue";

const groups = ref([
  {
    title: '项目1',
    id: 1,
    list: [
      {
        title: '项目1-1',
        id: 11
      },
      {
        title: '项目1-2',
        id: 21
      }
    ]
  },
  {
    title: '项目2',
    id: 2,
    list: [
      {
        title: '项目2-1',
        id: 12
      },
      {
        title: '项目2-2',
        id: 22
      }
    ]
  }
])

const choiseSideBar = item => {
  console.log(item)
}
const addGroupDialogVisible = ref(false)
const groupTitle = ref('')
const addGroup = () => {
  addGroupDialogVisible.value = true
}
const handleConfirmAddGroup = () => {
  addGroupDialogVisible.value = false
}
const addGroupItemDialogVisible = ref(false)
const groupItemTitle = ref('')

const add = obj => {
  console.log(obj)
  addGroupItemDialogVisible.value = true
}
const handleConfirmAddGroupItem = () => {
  addGroupItemDialogVisible.value = false
}
const edit = obj => {
  console.log(obj)
  console.log('edit')
}
const deletebyId = obj => {
  console.log(obj)
  console.log('delete')
}
// const getList = () => {
//   getGroups().then((res) => {
//     console.log(res);
//   });
// };
// getList();

console.log('--------------------------------@@@分割线@@@----------------------------');

</script>
<style lang="scss" scoped>
.leftContent {
  height: 100%;
  margin-right: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.rightContent {
  height: 100%;
  background-color: #fff;
  padding: 10px;
}
</style>
