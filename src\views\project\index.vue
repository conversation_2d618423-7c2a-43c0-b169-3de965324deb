<template>
    
      <el-splitter>
        <el-splitter-panel size="30%" :min="250">
          <div class="leftContent" style="height: calc(100vh - 80px)">
            <el-scrollbar>
              <ModelCard :projectId="itemId" :initData="projectItemInfo.model"/>
              <ProcessCard :projectId="itemId" :initData="projectItemInfo.methodList"  />
              <TaskCard :projectId="itemId" :initData="projectItemInfo.rulesetList" />
            </el-scrollbar>
          </div>
        </el-splitter-panel>
        <el-splitter-panel :min="400">
          <div class="rightContent">
            <!-- 规则顶部按钮 -->
            <div style="display: flex; justify-content: space-between;">   
              <div>
                <el-button v-if="rules.length == 0" type="primary" size="small" @click="addRule">
                  <el-icon>
                    <Plus />
                  </el-icon> 添加规则
                </el-button>
              </div> 
              <el-button type="primary" size="small" @click="saveRule">保存</el-button>
            </div>
            <!-- 规则内容 -->
            <div>
              <draggable 
                v-model="rules"
                item-key="id"
              >
                <template #item="{element}">
                  <Rule
                    :ruleData="element"
                    @handleRule="handleRule($event, element.id)"
                  ></Rule>
                </template>
              </draggable>
              
            </div>
            
            <!-- 规则底部按钮 -->
            <div style="margin-top: 10px;" v-if="rules.length!=0">
              <el-button type="primary" plain link @click="addRule">
                <el-icon>
                  <Plus />
                </el-icon> 添加规则
              </el-button>
            </div>
          </div>
        </el-splitter-panel>
      </el-splitter>
</template>
<script setup>
import { ref } from 'vue'
import ModelCard from './component/model.vue'
import ProcessCard from './component/process.vue'
import TaskCard from './component/task.vue'
import Rule from './component/rule.vue'

import { queryProjectItemInfo } from '@/api/project/index'

const props = defineProps({
  // itemid 就是 左边菜单栏点击项目或者模型 当前的id
  itemId: {
    type: [Number, String],
    required: true
  }
})

const projectItemInfo = ref({})

const getProjectItemInfo = async (projectId) => {
  const res = await queryProjectItemInfo({
    projectId: projectId
  })
  if(res.code !== 200 || !res.data) return;
  let { model,rulesetList,methodList } = res.data
  projectItemInfo.value = {
    model:model ? [model] : [],
    rulesetList: rulesetList ?? [],
    methodList : methodList ?? []
  }
}



watch(() => props.itemId, (newVal) => {
   if(newVal){
    getProjectItemInfo(newVal)
   }
})

console.log('--------------------------------@@@分割线@@@----------------------------');
import draggable from 'vuedraggable';

import configurationFn from './hooks/configuration'
const { operatorStore, 
        methodStore,
        modelStore, 
        getAllData, 
        addModel, 
        deleteModel, 
        addMethod, 
        deleteMethod} = configurationFn()
// 获取预设好的数据
getAllData([1, 2], [2,3])

import rulesFn from './hooks/rules.js'

const {rules, getRules} = rulesFn()
getRules()
const addRule = () => {
  
  rules.value.push({
    id: Date.now(),
    title: Date.now()
  })
}
const handleRule = (type, id) => {
  const index = rules.value.findIndex(item => item.id === id)
  console.log(type, index);
  switch (type){
    case 'add':
      rules.value.splice(index + 1, 0, {
        id: Date.now(),
        title: Date.now()
      })
      break;
    case 'delete':
      rules.value.splice(index, 1)
      break;
    default:
      break;
  }
}

const saveRule = () => {
  console.log(rules.value);
  
}

</script>
<style lang="scss" scoped>
.leftContent {
  height: 100%;
  margin-right: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.rightContent {
  height: 100%;
  background-color: #fff;
  padding: 10px;
  border-radius: 10px;
}
</style>
