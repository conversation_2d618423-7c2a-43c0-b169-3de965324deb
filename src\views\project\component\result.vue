<template>
  <div class="result-panel">
    <div class="panel-header">
      <h2 class="panel-title">结果</h2>
      <el-button
        circle
        size="small"
        class="collapse-btn"
        @click="isCollapsed = !isCollapsed"
      >
        <el-icon :size="16">
          <ArrowUp v-if="!isCollapsed" />
          <ArrowDown v-else />
        </el-icon>
      </el-button>
    </div>

    <div v-if="!isCollapsed" class="panel-content">
      <div class="form-row">
        <div class="form-label">结论</div>
        <el-select
          v-model="resultValue"
          placeholder="请选择结论"
          class="form-select"
        >
          <el-option label="通过" value="pass" />
          <el-option label="不通过" value="fail" />
          <el-option label="未知" value="unknown" />
        </el-select>

        <div class="form-switch-label">是否中止</div>
        <el-switch v-model="isTerminated" active-color="#E6A23C" />
      </div>

      <div class="form-row">
        <div class="form-label">描述</div>
        <el-input
          v-model="description"
          placeholder="请输入描述"
          class="form-textarea"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue";

// 控制折叠状态
const isCollapsed = ref(false);

// 表单数据
const resultValue = ref("");
const isTerminated = ref(true);
const description = ref("");
</script>

<style scoped lang="scss">
$bg-color: #f8f5e8;
$border-color: #eee9d9;
$theme-color: #e6a23c;
$text-dark: #333333;
$text-light: #666666;

.result-panel {
  margin-top: 20px;

  padding: 0 10px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  padding-top: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;

  .panel-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: $text-dark;
    letter-spacing: 0.5px;
  }
}

.collapse-btn {
  color: $text-light;

  &:hover {
    background-color: white;
    border-color: $theme-color;
  }

  .el-icon {
    font-weight: bold;
  }
}

.panel-content {
  padding: 8px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  // &:last-child {
  //   margin-bottom: 0;
  // }
}

.form-label {
  width: 70px;
  font-size: 14px;
  font-weight: 500;
  color: $text-dark;
  flex-shrink: 0;
}

.form-select {
  max-width: 180px;
  margin-right: 15px;

  :deep(.el-input__wrapper) {
    background-color: white;
    border-radius: 4px;
    border: 1px solid $border-color;
    box-shadow: none;

    &:hover {
      border-color: $theme-color;
    }

    .el-input__inner {
      color: $text-dark;
    }
  }

  :deep(.el-select__caret) {
    color: $text-light;
  }
}

.form-indicator {
  width: 24px;
  font-size: 16px;
  font-weight: bold;
  color: $theme-color;
  margin-right: 30px;
  flex-shrink: 0;
}

.form-switch-label {
  font-size: 14px;
  color: $text-light;
  margin-right: 12px;
  width: 70px;
}

:deep(.el-switch) {
  height: 24px;

  .el-switch__core {
    width: 48px;
    height: 24px;
    border-radius: 12px;
    background-color: #dcdfe6;
    border: none;

    &::after {
      width: 20px;
      height: 20px;
      top: 2px;
      left: 2px;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
  }

  &.is-checked .el-switch__core {
    background-color: $theme-color;

    &::after {
      transform: translateX(24px);
    }
  }
}

.form-textarea {
  width: 50%;

  :deep(.el-textarea__inner) {
    min-height: 90px;
    padding: 12px 15px;
    font-size: 14px;
    line-height: 1.6;
    background-color: white;
    border: 1px solid $border-color;
    border-radius: 4px;
    color: $text-dark;
    resize: vertical;
    box-shadow: none;

    &::placeholder {
      color: #c0c4cc;
    }

    &:focus {
      border-color: $theme-color;
      box-shadow: 0 0 0 1px rgba($theme-color, 0.2);
    }
  }
}
</style>
