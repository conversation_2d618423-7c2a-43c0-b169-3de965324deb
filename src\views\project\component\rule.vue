<template>
  <div class="rule-card">
    <div class="card-header">
      <!-- 左侧规则名称与编辑按钮 -->
      <div class="rule-header">
        <div v-if="!editing" class="rule-name-container" @click="startEditing">
          <span class="rule-name">{{ ruleName }}</span>
          <el-icon class="edit-icon"><Edit /></el-icon>
        </div>
        <el-input
          v-else
          ref="nameInput"
          v-model="tempRuleName"
          class="name-edit-input"
          size="small"
          @blur="saveRuleName"
          @keyup.enter="saveRuleName"
        />
      </div>
      
      <!-- 右侧操作按钮 -->
      <div class="header-actions">
        <el-dropdown trigger="click" @command="handleMenuCommand">
          <el-button icon class="more-btn" circle size="small">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="add">
                <el-icon><Plus /></el-icon>增加
              </el-dropdown-item>
              <el-dropdown-item command="delete" divided>
                <el-icon><Delete /></el-icon>删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <el-button 
          icon
          circle 
          size="small" 
          class="collapse-btn"
          @click="toggleCollapse"
        >
          <el-icon :size="16">
            <ArrowDown v-if="isCollapsed" />
            <ArrowUp v-else />
          </el-icon>
        </el-button>
      </div>
    </div>
    
    <!-- 内容区（支持上下折叠） -->
    <el-collapse-transition>
      <div v-show="!isCollapsed" class="card-content">
        <Condition
          v-for="(group, index) in groups" 
          :key="group.id"
          :groupIndex="index"
          @add-group="addGroup"
          @delete-group="removeGroup"
        />
        <Result></Result>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import {
  Edit, MoreFilled, ArrowUp, ArrowDown,
  Plus, Delete
} from '@element-plus/icons-vue'
import { ElCollapseTransition } from 'element-plus'
import Condition from './condition.vue'
import Result from './result.vue'
// 规则名称
const ruleName = ref('规则一')
const editing = ref(false)
const tempRuleName = ref('')
const nameInput = ref(null)

// 折叠状态
const isCollapsed = ref(false)

// 开始编辑规则名称
const startEditing = () => {
  editing.value = true
  tempRuleName.value = ruleName.value
  nextTick(() => {
    nameInput.value.focus()
  })
}

// 保存规则名称
const saveRuleName = () => {
  if (tempRuleName.value.trim() !== '') {
    ruleName.value = tempRuleName.value.trim()
  }
  editing.value = false
}

// 处理菜单命令
const handleMenuCommand = (command) => {
  if (command === 'add') {
    emit('add')
  } else if (command === 'delete') {
    emit('delete')
  }
}

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

const groups = ref([{ id: Date.now() }]);
    
const addGroup = () => {
  groups.value.push({ id: Date.now() });
};

const removeGroup = (index) => {
  if (groups.value.length > 1) {
    groups.value.splice(index, 1);
  }
};

const emit = defineEmits(['add', 'delete'])
</script>

<style scoped lang="scss">
.rule-card {
  margin-top: 10px;
  width: 100%;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #eaeaea;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #efefef;
}

.rule-header {
  display: flex;
  align-items: center;
  
  .rule-name-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }
  }
  
  .rule-name {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    margin-right: 8px;
  }
  
  .edit-icon {
    color: #666666;
    font-size: 14px;
  }
  
  .name-edit-input {
    width: 180px;
    
    :deep(.el-input__wrapper) {
      background-color: #ffffff;
      border: 1px solid #409eff;
      box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
    }
  }
}

.header-actions {
  display: flex;
  gap: 8px;
  
  .more-btn, .collapse-btn {
    background-color: rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.08);
    
    .el-icon {
      color: #333333;
    }
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.08);
      border-color: rgba(0, 0, 0, 0.12);
    }
  }
  
  .collapse-btn {
    .el-icon {
      transition: transform 0.3s;
    }
  }
}

.card-content {
  padding: 20px;
  background-color: #ffffff;
  border-top: 1px solid #efefef;
}

// 动画优化
:deep(.el-collapse-transition) {
  transition: max-height 0.3s ease-in-out;
  overflow: hidden;
}
</style>