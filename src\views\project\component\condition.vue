<template>
  <div class="condition-container">
    <!-- 主标题区域 -->
    <div class="main-header">
      <div class="main-title">
        <span class="title-text">如果</span>
        <!-- 操作按钮区 -->
        <div class="header-actions">
          <el-button
            circle
            size="small"
            class="collapse-btn"
            @click="isCollapsed = !isCollapsed"
          >
            <el-icon :size="16">
              <ArrowUp v-if="!isCollapsed" />
              <ArrowDown v-else />
            </el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 条件组区域 -->
    <div v-show="!isCollapsed" class="condition-content">
      <div
        v-for="(group, groupIndex) in groups"
        :key="groupIndex"
        class="condition-group"
      >
        <div class="group-header">
          <!-- 组间逻辑连接器 -->
          <div v-if="groupIndex > 0" class="group-connector">
            <el-select
              v-model="group.relation"
              placeholder="请选择"
              size="small"
              class="relation-select"
            >
              <el-option label="且" value="and" />
              <el-option label="或" value="or" />
            </el-select>
          </div>
        </div>

        <div class="group-content">
          <div
            v-for="(condition, condIndex) in group.conditions"
            :key="condIndex"
            class="condition-row"
          >
            <!-- 条件序号 -->
            <div class="condition-order">
              <span>{{ condIndex + 1 }}</span>
            </div>

            <!-- 行内逻辑连接器 -->
            <div v-if="condIndex > 0" class="row-connector">
              <el-select
                v-model="condition.relation"
                placeholder="请选择"
                size="small"
                class="relation-select"
              >
                <el-option label="且" value="and" />
                <el-option label="或" value="or" />
              </el-select>
            </div>

            <!-- 内容区 -->
            <div class="condition-body">
              <!-- 类型选择 -->
              <el-select
                v-model="condition.firstType"
                placeholder="请选择类型"
                class="condition-select"
                clearable
              >
                <el-option label="参数" value="parameter" />
                <el-option label="方法" value="method" />
              </el-select>

              <!-- 参数/方法选择器 -->
              <el-cascader
                v-if="condition.firstType"
                v-model="condition.firstValue"
                :placeholder="
                  condition.firstType === 'parameter'
                    ? '请选择参数'
                    : '请选择方法'
                "
                :options="firstOptions[condition.firstType]"
                class="condition-select"
                clearable
              />
              <div v-else class="condition-placeholder"></div>

              <!-- 运算符 -->
              <el-cascader
                v-model="condition.operator"
                :options="operatorOptions"
                placeholder="请选择运算符"
                class="condition-select"
                clearable
              />
              <div
                v-if="!condition.operator || condition.operator.length === 0"
                class="condition-placeholder"
              ></div>

              <!-- 输入类型选择 -->
              <el-select
                v-model="condition.secondType"
                placeholder="请选择类型"
                class="condition-select"
                clearable
              >
                <el-option label="输入" value="input" />
                <el-option label="参数" value="parameter" />
                <el-option label="方法" value="method" />
              </el-select>

              <!-- 动态输入部分 -->
              <el-cascader
                v-if="condition.secondType && condition.secondType !== 'input'"
                v-model="condition.secondValue"
                :placeholder="
                  condition.secondType === 'parameter'
                    ? '请选择参数'
                    : '请选择方法'
                "
                :options="secondOptions[condition.secondType]"
                class="condition-select"
                clearable
              />
              <el-input
                v-else-if="condition.secondType === 'input'"
                v-model="condition.inputValue"
                placeholder="请输入"
                class="condition-input"
                clearable
              />
              <div v-else class="condition-placeholder"></div>
            </div>

            <!-- 操作按钮区 -->
            <div class="condition-actions">
              <!-- 行内添加按钮 -->
              <el-button
                circle
                size="small"
                class="add-btn"
                @click="addConditionBelow(groupIndex, condIndex)"
              >
                <el-icon :size="14">
                  <Plus />
                </el-icon>
              </el-button>

              <!-- 删除按钮（首行不显示） -->
              <el-button
                v-if="condIndex > 0 || group.conditions.length > 1"
                circle
                size="small"
                class="remove-btn"
                @click="removeCondition(groupIndex, condIndex)"
              >
                <el-icon :size="14">
                  <Minus />
                </el-icon>
              </el-button>
            </div>
          </div>

          <!-- 组内底部添加按钮 -->
          <div class="add-condition" @click="addCondition(groupIndex)">
            <el-icon class="add-icon">
              <Plus />
            </el-icon>
            <span>增加组内条件</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 添加条件组按钮 -->
  <div class="add-group-container">
    <div class="add-group" @click="addGroup">
      <el-icon class="add-icon">
        <Plus />
      </el-icon>
      <span>增加条件组</span>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  ArrowUp,
  ArrowDown,
  MoreFilled,
  Plus,
  Delete,
  Minus,
} from "@element-plus/icons-vue";

// 折叠状态
const isCollapsed = ref(false);

// 初始化级联选择数据
const firstOptions = {
  parameter: [
    {
      value: "user",
      label: "用户",
      children: [
        { value: "name", label: "姓名" },
        { value: "age", label: "年龄" },
        { value: "email", label: "邮箱" },
      ],
    },
    {
      value: "order",
      label: "订单",
      children: [
        { value: "id", label: "订单号" },
        { value: "amount", label: "金额" },
        { value: "date", label: "日期" },
      ],
    },
  ],
  method: [
    {
      value: "calculate",
      label: "计算方法",
      children: [
        { value: "average", label: "平均值" },
        { value: "sum", label: "求和" },
        { value: "max", label: "最大值" },
      ],
    },
    {
      value: "string",
      label: "字符串方法",
      children: [
        { value: "concat", label: "连接" },
        { value: "length", label: "长度" },
        { value: "substr", label: "子字符串" },
      ],
    },
  ],
};

const secondOptions = {
  ...firstOptions,
};

// 运算符选项
const operatorOptions = [
  {
    value: "equality",
    label: "比较运算",
    children: [
      { value: "eq", label: "等于" },
      { value: "ne", label: "不等于" },
      { value: "gt", label: "大于" },
      { value: "lt", label: "小于" },
    ],
  },
  {
    value: "logical",
    label: "逻辑运算",
    children: [
      { value: "and", label: "并且" },
      { value: "or", label: "或者" },
      { value: "not", label: "非" },
    ],
  },
  {
    value: "membership",
    label: "成员运算",
    children: [
      { value: "in", label: "包含" },
      { value: "not_in", label: "不包含" },
    ],
  },
];

// 初始化组数据
const groups = ref([
  {
    conditions: [
      {
        firstType: "",
        firstValue: [],
        operator: [],
        secondType: "",
        secondValue: [],
        inputValue: "",
        relation: "and", // 行内逻辑关系
      },
    ],
    relation: "and", // 组间逻辑关系
  },
]);

// 在指定行下方添加条件
const addConditionBelow = (groupIndex, condIndex) => {
  groups.value[groupIndex].conditions.splice(condIndex + 1, 0, {
    firstType: "",
    firstValue: [],
    operator: [],
    secondType: "",
    secondValue: [],
    inputValue: "",
    relation: "and",
  });
};

// 在组末尾添加条件
const addCondition = (groupIndex) => {
  groups.value[groupIndex].conditions.push({
    firstType: "",
    firstValue: [],
    operator: [],
    secondType: "",
    secondValue: [],
    inputValue: "",
    relation: "and",
  });
};

// 移除条件
const removeCondition = (groupIndex, condIndex) => {
  if (groups.value[groupIndex].conditions.length > 1) {
    groups.value[groupIndex].conditions.splice(condIndex, 1);
  }
};

// 处理操作命令
const handleCommand = (command) => {
  if (command === "deleteGroup") {
    if (groups.value.length > 1) {
      groups.value.splice(groups.value.length - 1, 1);
    }
  }
};

// 添加新条件组
const addGroup = () => {
  groups.value.push({
    conditions: [
      {
        firstType: "",
        firstValue: [],
        operator: [],
        secondType: "",
        secondValue: [],
        inputValue: "",
        relation: "and",
      },
    ],
    relation: "and",
  });
};
</script>

<style scoped lang="scss">
$yellow-theme: #e6a23c;
$yellow-light: #fdf6ec;
$yellow-dark: #d48836;
$amber-bg: #f8f5e8;
$amber-light: #fcf7e5;
$amber-border: #f0e4c2;
$amber-orange: #e6a23c;

.condition-container {
  // max-width: 1200px;
  // margin: 0 auto;
  padding: 0 10px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

.main-header {
  padding: 16px 0;
  position: relative;
  margin-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.main-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-text {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    position: relative;
  }
}

.header-actions {
  display: flex;
  gap: 8px;

  .collapse-btn {
    color: #333;

    &:hover {
      background-color: white;
      border-color: $yellow-theme;
    }

    .el-icon {
      font-weight: bold;
    }
  }
}

.condition-content {
  display: flex;
  flex-direction: column;
}

.condition-group {
  border-radius: 8px;
  overflow: hidden;
}

.group-header {
  // background-color: #e64c4c;
  margin-top: 5px;
  margin-left: -20px;
}

.group-connector {
  margin-left: 20px;

  .relation-select {
    width: 70px;
  }
}

.group-content {
  padding: 16px;
}

.condition-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.condition-order {
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $amber-orange;
  color: white;
  border-radius: 50%;
  margin-right: 12px;
  font-size: 13px;
  flex-shrink: 0;
}

.row-connector {
  margin-right: 10px;

  .relation-select {
    width: 60px;
  }
}

.condition-body {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
  align-items: center;
}

.condition-select,
:deep(.el-cascader) {
  min-width: 130px;
  flex: 1;
  max-width: 180px;
}

.condition-input {
  flex: 1;
  max-width: 180px;
}

.condition-placeholder {
}

.condition-actions {
  display: flex;
  gap: 6px;
  margin-left: 10px;
  flex-shrink: 0;

  .el-button {
    width: 26px;
    height: 26px;
    padding: 0;

    &.add-btn {
      background-color: $amber-orange;
      color: white;
      border: none;

      &:hover {
        background-color: $yellow-dark;
      }
    }

    &.remove-btn {
      background-color: #f56c6c;
      color: white;
      border: none;

      &:hover {
        background-color: #e64c4c;
      }
    }
  }
}

.add-condition,
.add-group {
  display: inline-flex;
  align-items: center;
  color: $amber-orange;
  font-weight: 400;
  font-size: 14px;
  cursor: pointer;
  padding: 5px 0;
  transition: all 0.2s;

  .add-icon {
    margin-right: 3px;
    font-weight: bold;
    font-size: 12px;
  }

  &:hover {
    background-color: rgba($amber-orange, 0.15);
    border-color: $amber-orange;
  }
}

.add-group-container {
}

.add-group {
  min-width: 200px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
