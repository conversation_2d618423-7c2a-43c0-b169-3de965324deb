# Sidebar 组件使用文档

## 1. 基本介绍
`Sidebar` 是一个通用的左侧导航栏组件，支持：
- 按分组展示列表
- 搜索过滤
- 有二级菜单（`list`）和无二级菜单（直接单项）
- 新增、编辑、删除分组或条目
- 默认激活第一项，点击高亮

---

## 2. Props 参数

| 参数名   | 类型     | 默认值 | 说明 |
|----------|----------|--------|------|
| `title`  | String   | `''`   | 标题，会显示为 `{title}列表` |
| `operate`| Array    | `[]`   | 控制功能按钮显示，支持值：`addGroup`（新建分组）、`add`（新建条目）、`edit`（编辑）、`delete`（删除） |
| `groups` | Array    | `[]`   | 数据源数组，支持有 `list`（分组+子项）和无 `list`（直接单项） |

---

## 3. `groups` 数据结构示例

### 有 list（分组 + 子项）
```json
[
  {
    "id": "group1",
    "title": "分组1",
    "list": [
      { "id": "item1", "title": "子项1" },
      { "id": "item2", "title": "子项2" }
    ]
  }
]
```

### 无 list（直接单项）
```json
[
  { "id": "group1", "title": "分组1" },
  { "id": "group2", "title": "分组2" }
]
```

---

## 4. 事件（Emits）

| 事件名     | 参数说明 | 触发时机 |
|------------|----------|----------|
| `choise`   | `{ item }`：点击的项对象（分组或子项） | 点击菜单项时触发 |
| `addGroup` | 无       | 点击新建分组按钮时触发 |
| `add`      | `group`：当前分组对象 | 点击分组下的新建条目按钮时触发 |
| `edit`     | `item`：当前对象 | 点击编辑按钮时触发 |
| `delete`   | `item`：当前对象 | 点击删除按钮时触发 |

---

## 5. 返回参数说明

### `choise`
```js
{
  item: {
    id: 'itemId',   // String / Number，唯一标识
    title: '名称',  // String
    // 如果是分组，可能还有 list 属性
  }
}
```
说明：点击分组（无 list）或子项时触发，`item` 就是对应对象。

### `addGroup`
- 无参数  
说明：新建分组按钮点击后触发，由外部接管新增逻辑。

### `add`
```js
{
  id: 'groupId',
  title: '分组名称',
  list: [ ... ] // 当前分组下的子项数组
}
```
说明：点击分组右侧“新建项目”按钮时触发，返回当前分组对象。

### `edit`
```js
{
  id: 'itemId',
  title: '名称',
  // 如果是分组，可能还有 list
}
```
说明：点击编辑按钮时触发，返回当前对象。

### `delete`
```js
{
  id: 'itemId',
  title: '名称',
  // 如果是分组，可能还有 list
}
```
说明：点击删除按钮时触发，返回当前对象。

---

## 6. 使用示例

```vue
<template>
  <Sidebar
    title="项目"
    :operate="['addGroup', 'add', 'edit', 'delete']"
    :groups="groupList"
    @choise="handleChoise"
    @addGroup="handleAddGroup"
    @add="handleAddItem"
    @edit="handleEdit"
    @delete="handleDelete"
  />
</template>

<script setup>
import Sidebar from './Sidebar.vue'

const groupList = [
  {
    id: 'g1',
    title: '分组1',
    list: [
      { id: 'i1', title: '子项1' },
      { id: 'i2', title: '子项2' }
    ]
  },
  { id: 'g2', title: '分组2' }
]

const handleChoise = ({ item }) => {
  console.log('选择了：', item)
}

const handleAddGroup = () => {
  console.log('新建分组')
}

const handleAddItem = group => {
  console.log('在分组中新建项目：', group)
}

const handleEdit = item => {
  console.log('编辑：', item)
}

const handleDelete = item => {
  console.log('删除：', item)
}
</script>
```
