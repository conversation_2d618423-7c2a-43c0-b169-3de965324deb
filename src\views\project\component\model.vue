<template>
  <div class="modelCard collapse-card">
    <el-collapse v-model="activePanel">
      <el-collapse-item name="model">
        <template #title>
          <div class="cardheader" @click.stop="() => {}">
            <h2 class="header_title">模型</h2>
            <div class="header_action">
              <el-button
                v-if="modelData.length === 0"
                link
                type="primary"
                @click="handleAdd"
                >新增</el-button
              >
            </div>
          </div>
        </template>
        <CustomTable
          class="table-container"
          :tableData="modelData"
          :columns="modelColumns"
        >
          <template #action="{ row }">
            <el-popover
              class="box-item"
              placement="right-start"
              trigger="hover"
              popper-style="padding: 5px  10px;"
            >
              <template #reference>
                <el-icon :size="13"><MoreFilled /></el-icon>
              </template>
              <div class="pop-content">
                <div class="pop-item" @click="handleEdit">
                  <el-icon :size="14"><Edit /></el-icon>
                  <span>编辑</span>
                </div>
                <div class="pop-item" @click="handleDelete">
                  <el-icon :size="14"><Delete /></el-icon>
                  <span>删除</span>
                </div>
              </div>
            </el-popover>
          </template>
        </CustomTable>
      </el-collapse-item>
    </el-collapse>
    <el-dialog
      v-model="modelDialogVisible"
      :title="title"
      width="30%"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">{{ title }}</span>
        </div>
      </template>
      <el-form ref="formRef" :model="formData" label-width="80px">
        <el-form-item label="模型名称" prop="name">
          <el-select v-model="formData.name" placeholder="请选择模型">
            <el-option
              v-for="item in modelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
const { proxy } = getCurrentInstance();


const props = defineProps({
  projectId: {
    type: [Number, String],
    required: true
  },
  initData:{
    type: Array,
    default: () => []
  }
})


const activePanel = ref("model");

const formRef = ref(null);
const title = ref("添加模型");
const modelDialogVisible = ref(false);

const formData = ref({});
const modelOptions = ref([
  {
    label: "模型1",
    value: "1",
  },
  {
    label: "模型2",
    value: "2",
  },
]);

const modelColumns = ref([
  { label: "模型名称", prop: "nameZh", align: "left" },
  { label: "", slot: "action", width: "40", align: "center", fixed: "right" },
]);

const modelData = ref([...props.initData]);


watch(
  () => props.initData,
  newVal => {
    modelData.value = [...newVal];
  },
  { deep: true }
)


const handleAdd = () => {
  resetForm();
  title.value = "添加模型";
  modelDialogVisible.value = true;
};

const handleEdit = () => {
  resetForm();
  title.value = "编辑模型";
  modelDialogVisible.value = true;
};

const resetForm = () => {
  formData.value = {
    name: "",
  };
  formRef.value?.resetFields();
};

const handleDelete = (row) => {
  proxy.$modal.confirm("是否确认删除该模型？").then(() => {
    modelData.value = [];
    proxy.$modal.msgSuccess("删除成功");
  });
};
const cancel = () => {
  modelDialogVisible.value = false;
  resetForm();
};
const submitForm = () => {
  proxy.$modal.confirm("是否确认提交数据？").then(() => {
    modelData.value.push({
      id: modelData.value.length + 1,
      name: "模型xxx",
    });
    modelDialogVisible.value = false;
    resetForm();
  });
};
</script>
<style lang="scss" scoped>
.modelCard {
  background-color: #fff;
  padding: 0px 10px;
  margin-bottom: 20px;
  .el-collapse {
    --el-collapse-border-color: transparent;
  }
  .cardheader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header_title {
      color: #09090B;
      font-size: 16px;
    }
    .header_action {
      margin-right: 10px;
    }
  }
  .table-container {
    :deep(.cell) {
      padding: 0px;
    }
  }
}
.pop-content {
  display: flex;
  flex-direction: column;
  .pop-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    height: 24px;
    cursor: pointer;
    span {
      margin-left: 10px;
    }
    &:hover {
      background: #fef6e8;
    }
  }
}

.el-dialog {
  .dialog-header {
    border-left: 2px solid #f6a123;
    padding-left: 10px;
    .dialog-title {
      color: #f6a123;
    }
  }
  .el-form {
    width: 80%;
    margin: 10px auto;
  }
}
</style>
