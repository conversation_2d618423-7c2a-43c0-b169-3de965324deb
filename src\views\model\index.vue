<template>
  <div class="model-content">
    <CustomTable
      class="table-container"
      :loading="loading"
      :tableData="modelData"
      :columns="modelColumns"
    ></CustomTable>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
import { getModelDetail } from "@/api/model";

const props = defineProps({
  itemId: {
    type: [Number, String],
    required: true,
  },
});

const loading = ref(false);

const modelColumns = ref([
  {
    label: "字段中文名称",
    prop: "nameZh",
  },
  {
    label: "字段参数",
    prop: "name",
  },
  {
    label: "参数类型",
    prop: "dataType",
  },
  {
    label: "描述说明",
    prop: "label",
  },
]);
const modelData = ref([]);

const getModelData = (modelId) => {
  loading.value = true;
  getModelDetail(modelId)
    .then((res) => {
      modelData.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
};

watch(
  () => props.itemId,
  (newVal) => {
    if (newVal) {
      getModelData(newVal);
    }
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped>
.model-content {
  width: 100%;
  min-height: calc(100vh - 74px);
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  .table-container {
    flex: 1;
  }
}
</style>
