<template>
  <div class="container">
    <RSideBar :groups="groups" ></RSideBar>
    <div class="main-content">
      <div class="model-content">
        <CustomTable class="table-container" :loading="loading" :tableData="modelData" :columns="modelColumns"></CustomTable>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import RSideBar from "@/components/RSideBar.vue";
import CustomTable from "@/components/CustomTable/index.vue";

const props = defineProps({
  modelId: {
    type: Number,
    default:1,
    required: true,
  },
})

const groups = ref([
  {
    title: "模型1",
    id: 1,
    list: [
      {
        title: "模型1-1",
        id: 11,
      },
      {
        title: "模型1-2",
        id: 21,
      },
    ],
  },
  {
    title: "模型2",
    id: 2,
    list: [
      {
        title: "模型2-1",
        id: 12,
      },
      {
        title: "模型2-2",
        id: 22,
      },
    ],
  },
]);
const loading = ref(true);

const modelColumns = ref([
  {
    label: "字段中文名称",
    prop: "cparam",
  },
  {
    label: "字段参数",
    prop: "param",
  },
  {
    label: "参数类型",
    prop: "type",
  },
  {
    label: "描述说明",
    prop: "desc",
  },
])
const modelData = ref([
  {
    cparam: "姓名",
    param: "name",
    type: "String",
    desc: "用户真实姓名，最长为20个字符",
  },
  {
    cparam: "年龄",
    param: "age",
    type: "Number",
    desc: "用户的年龄，最长为3位数字",
  },
]);

const getModelData = () => {
  loading.value = true;
  setTimeout(() => {
    modelData.value = [...modelData.value];
    loading.value = false;
  }, 1000);
};

watch(
  () => props.modelId,
  (newVal) => {
    getModelData();
  },
  {
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
.model-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .table-container {
    flex: 1;
  }
}
</style>
