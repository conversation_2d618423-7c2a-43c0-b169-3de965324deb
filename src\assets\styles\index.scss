*{
    margin: 0;
    box-sizing: border-box;
    padding: 0;
    font-family: Arial, sans-serif;
}
.container{
    display: flex;
}
.main-content{
    flex:1;
    background: #f1f3f5;
    padding: 15px;
}

/* fade-transform */
.fade-transform--move,
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}