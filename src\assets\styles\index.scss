*{
    margin: 0;
    box-sizing: border-box;
    padding: 0;
    font-family: Arial, sans-serif;
}
.container{
    display: flex;
}
.main-container{
  width: calc(100% - 250px);
  height: calc(100vh - 50px);
  background: #f1f3f5;
}
.main-content{
    // flex:1;    
    width: 100%;
    height: 100%;
    background: #f1f3f5;
    padding: 12px;
}

.collapse-card{
  border-radius: 10px;
  .card-title{
    color: #09090B;
    font-size: 16px;
  }
}

/* fade-transform */
.fade-transform--move,
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
