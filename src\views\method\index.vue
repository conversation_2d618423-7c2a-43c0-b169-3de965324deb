<template>
  <div class="container">
    <RSideBar :groups="groups"></RSideBar>
    <div class="main-content">
      <div class="method-content">
        <div class="title">输入</div>
        <CustomTable
          class="table-container"
          :loading="loading"
          :tableData="inputTableData"
          :columns="inputTableColumns"
        ></CustomTable>
        <div class="title">输出</div>
        <CustomTable
          class="table-container"
          :loading="loading"
          :tableData="outputTableData"
          :columns="outTableColumns"
        ></CustomTable>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import RSideBar from "@/components/RSideBar.vue";
import CustomTable from "@/components/CustomTable/index.vue";
const props = defineProps({
  methodId: {
    type: Number,
    required: true,
  },
});
const groups = ref([
  {
    title: "模型1",
    id: 1,
    list: [
      {
        title: "模型1-1",
        id: 11,
      },
      {
        title: "模型1-2",
        id: 21,
      },
    ],
  },
  {
    title: "模型2",
    id: 2,
    list: [
      {
        title: "模型2-1",
        id: 12,
      },
      {
        title: "模型2-2",
        id: 22,
      },
    ],
  },
]);
const loading = ref(false);

// 输入参数表格数据
const inputTableColumns = ref([
  {
    label: "输入名称",
    prop: "inputName",
  },
  {
    label: "输入参数",
    prop: "inputParam",
  },
  {
    label: "参数类型",
    prop: "type",
  },
  {
    label: "描述说明",
    prop: "desc",
  },
]);
const inputTableData = ref([
  {
    inputName: "人像面图片",
    inputParam: "img",
    type: "File",
    desc: "图片的路径，例如：/path/to/image.jpg",
  },
]);

// 输出参数表格数据
const outTableColumns = ref([
  {
    label: "输出名称",
    prop: "outputName",
  },
  {
    label: "输出参数",
    prop: "outputParam",
  },
  {
    label: "参数类型",
    prop: "type",
  },
  {
    label: "描述说明",
    prop: "desc",
  },
]);
const outputTableData = ref([
  {
    outputName: "姓名",
    outputParam: "name",
    type: "String",
    desc: "用户真实姓名，最长为20个字符",
  },
  {
    outputName: "年龄",
    outputParam: "age",
    type: "Number",
    desc: "用户年龄，>18岁",
  },
  {
    outputName: "性别",
    outputParam: "gender",
    type: "String",
    desc: "用户性别，男/女",
  },
]);

const getMethodData = () => {
  loading.value = true;
  setTimeout(() => {
    inputTableData.value = [...inputTableData.value];
    outputTableData.value = [...outputTableData.value];
    loading.value = false;
  }, 1000);
};

watch(
  () => props.methodId,
  (newVal) => {
    getMethodData();
  },
  {
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
.method-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .title {
    font-size: 21px;
    font-weight: 600;
    color: #606266;
    margin-bottom: 10px;
  }
  .table-container {
    margin-bottom: 30px;
  }
}
</style>
