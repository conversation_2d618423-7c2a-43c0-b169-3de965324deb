<template>
  <div class="method-content">
    <div class="title">输入</div>
    <CustomTable
      class="table-container"
      :loading="loading"
      :tableData="inputTableData"
      :columns="inputTableColumns"
    ></CustomTable>
    <div class="title">输出</div>
    <CustomTable
      class="table-container"
      :loading="loading"
      :tableData="outputTableData"
      :columns="outTableColumns"
    ></CustomTable>
  </div>
</template>
<script setup>
import CustomTable from "@/components/CustomTable/index.vue";
import { getMethodDetail } from "@/api/method/index.js";
const props = defineProps({
  itemId: {
    type: [Number, String],
    required: true,
  },
});
const loading = ref(false);

// 输入参数表格数据
const inputTableColumns = ref([
  {
    label: "输入名称",
    prop: "nameZh",
  },
  {
    label: "输入参数",
    prop: "queryName",
  },
  {
    label: "参数类型",
    prop: "paramType",
  },
  {
    label: "描述说明",
    prop: "description",
  },
]);
const inputTableData = ref([]);

// 输出参数表格数据
const outTableColumns = ref([
  {
    label: "输出名称",
    prop: "nameZh",
  },
  { 
    label: "输出参数",
    prop: "queryName",
  },
  {
    label: "参数类型",
    prop: "paramType",
  },
  {
    label: "所属模型",
    prop: "modelName",
  },
  {
    label: "描述说明",
    prop: "description",
  },
]);
const outputTableData = ref([]);

const getMethodData = (methodId) => {
  loading.value = true;
  getMethodDetail(methodId)
    .then((res) => {
      if (res.code === 200 && res.data) {
        inputTableData.value = res.data.httpQuerysVo;
        outputTableData.value = res.data.outputParams;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

watch(
  () => props.itemId,
  (newVal) => {
    if (newVal) {
      getMethodData(newVal);
    }
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped>
.method-content {
  width: 100%;
  min-height: calc(100vh - 74px);
  padding: 20px;
  border-radius: 10px;
  background-color: #fff;
  .title {
    font-size: 21px;
    font-weight: 600;
    color: #606266;
    margin-bottom: 10px;
  }
  .table-container {
    margin-bottom: 30px;
  }
}
</style>
