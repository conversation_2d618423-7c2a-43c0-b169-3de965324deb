<template>
  <div class="app-container">
    <r-header></r-header>
    <router-view v-slot="{ Component, route }">
      <!-- <transition name="fade-transform" mode="out-in"> -->
        <!-- <keep-alive > -->
          <component
            :is="Component"
            :key="route.path"
          />
        <!-- </keep-alive> -->
      <!-- </transition> -->
    </router-view>
  </div>
</template>
<script setup>
import { reactive, ref } from 'vue'
import RHeader from '@/components/RHeader.vue'
</script>
<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-direction: column;
  .app-content {
    padding: 20px;
  }
}
</style>
