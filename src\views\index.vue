<template>
  <div class="app-container">
    <r-header></r-header>
    <div class="container">
      <RSideBar :title="title" :type="currentOption" @choise="choiseSideBar"></RSideBar>
      <div class="main-container">
        <el-scrollbar>
          <div class="main-content">
            <router-view v-slot="{ Component, route }">
              <transition name="fade-transform" mode="out-in">
                <keep-alive>
                  <component :is="Component" :key="route.path" :itemId="itemId" />
                </keep-alive>
              </transition>
            </router-view>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <!-- 编辑弹窗 -->
    <!-- <el-dialog title="编辑" v-model="dialogVisible" width="30%">
      <el-input v-model="inputTitle" placeholder="请输入新名称" clearable />
      <template #footer>
        <span>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmRename">确认</el-button>
        </span>
      </template>
    </el-dialog> -->

    
  </div>
</template>
<script setup>
import { reactive, ref, watch } from 'vue'
import RHeader from '@/components/RHeader.vue'
import RSideBar from '@/components/RSideBar.vue'

const ruleObj = {
  project: '项目',
  model: '模型',
  method: '方法',
  dictionary: '数据字典'
}
const route = useRoute()
const currentOption = ref('')
currentOption.value = route.name
const title = ref('')
title.value = ruleObj[route.name]


watch(route, newRoute => {
  currentOption.value = newRoute.name
  title.value = ruleObj[newRoute.name]
})

const itemId = ref('')

const choiseSideBar = item => {
  itemId.value = item.id
}

</script>
<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-direction: column;
  .app-content {
    padding: 20px;
  }
}
</style>
